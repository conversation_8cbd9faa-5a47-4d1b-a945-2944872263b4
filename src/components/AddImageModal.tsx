
import React, { useState } from 'react';
import { X } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { StepNavigation } from './modal/StepNavigation';
import { ImageSetupStep } from './modal/ImageSetupStep';
import { GroundTruthStep } from './modal/GroundTruthStep';
import { ReviewStep } from './modal/ReviewStep';
import type { TestImage, ParameterDefinitions } from '@/types/ergonomic';

interface AddImageModalProps {
  onAddImage: (image: TestImage) => void;
  onClose: () => void;
}

export const AddImageModal: React.FC<AddImageModalProps> = ({ onAddImage, onClose }) => {
  const [step, setStep] = useState(1);
  const [imageUrl, setImageUrl] = useState('');
  const [imageName, setImageName] = useState('');
  const [notes, setNotes] = useState('');
  const [groundTruth, setGroundTruth] = useState<Partial<ParameterDefinitions>>({});
  const [imageLoaded, setImageLoaded] = useState(false);

  const parameterConfig = {
    properWorkstation: { type: 'boolean', label: 'Proper Workstation' },
    properSideProfileImage: { type: 'boolean', label: 'Proper Side Profile Image' },
    facingSide: { 
      type: 'select', 
      label: 'Facing Side', 
      options: ['left', 'right', 'front', 'back'] 
    },
    legsVisible: { type: 'boolean', label: 'Legs Visible' },
    posture: { 
      type: 'select', 
      label: 'Posture', 
      options: ['Sitting back', 'perched forward', 'slouching', 'leaning forward'] 
    },
    sittingHeight: { 
      type: 'select', 
      label: 'Sitting Height', 
      options: ['hips lower than knees', 'hips inline with knees', 'Hips higher than knees legs grounded', 'Hips higher than knees legs hanging'] 
    },
    deskHeight: { 
      type: 'select', 
      label: 'Desk Height', 
      options: ['inline with elbow', 'above elbow', 'below elbow'] 
    },
    armrest: { 
      type: 'select', 
      label: 'Armrest', 
      options: ['above elbow', 'below elbow', 'inline with elbow', 'no armrests'] 
    },
    seatpan: { 
      type: 'select', 
      label: 'Seatpan', 
      options: ['2 finger spacing', 'More than 2 finger spacing', 'less than 2 finger spacing'] 
    },
    numberOfMonitors: { type: 'number', label: 'Number of Monitors' },
    numberOfLaptops: { type: 'number', label: 'Number of Laptops' },
    keyboard: { type: 'number', label: 'Keyboard Count' },
    mouse: { type: 'number', label: 'Mouse Count' },
    laptopStatus: { 
      type: 'select', 
      label: 'Laptop Status', 
      options: ['open', 'closed'] 
    },
    laptopUsage: { 
      type: 'select', 
      label: 'Laptop Usage', 
      options: ['open', 'docked'] 
    },
    screenDistance: { 
      type: 'select', 
      label: 'Screen Distance', 
      options: ['At arms length', 'Less than arms length', 'More than arms length'] 
    },
    laptopDistance: { 
      type: 'select', 
      label: 'Laptop Distance', 
      options: ['At arms length', 'Less than arms length', 'More than arms length'] 
    },
    screenHeight: { 
      type: 'select', 
      label: 'Screen Height', 
      options: ['below eye level', 'at eye level', 'above eye level'] 
    },
    laptopHeight: { 
      type: 'select', 
      label: 'Laptop Height', 
      options: ['below eye level', 'at eye level', 'above eye level'] 
    },
    keyboardPlacement: { 
      type: 'select', 
      label: 'Keyboard Placement', 
      options: ['Palm distance from edge', 'Extended and reaching forward', 'Edge of the table'] 
    },
    mousePlacement: { 
      type: 'select', 
      label: 'Mouse Placement', 
      options: ['Palm distance from edge', 'Extended and reaching forward', 'Edge of the table'] 
    }
  };

  const totalParameters = Object.keys(parameterConfig).length;
  const completedParameters = Object.keys(groundTruth).length;

  const handleImageLoad = () => {
    setImageLoaded(true);
    if (!imageName && imageUrl) {
      const urlParts = imageUrl.split('/');
      const filename = urlParts[urlParts.length - 1];
      setImageName(filename.replace(/\.[^/.]+$/, ''));
    }
  };

  const handleParameterChange = (key: string, value: any) => {
    setGroundTruth(prev => ({ ...prev, [key]: value }));
  };

  const isComplete = () => {
    return Object.keys(parameterConfig).every(key => groundTruth.hasOwnProperty(key));
  };

  const handleSubmit = () => {
    if (!isComplete()) return;

    const newImage: TestImage = {
      id: Date.now().toString(),
      url: imageUrl,
      name: imageName || 'Untitled Image',
      groundTruth: groundTruth as ParameterDefinitions,
      metadata: {
        addedBy: 'User',
        addedAt: new Date().toISOString(),
        notes: notes || undefined
      }
    };

    onAddImage(newImage);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-6xl max-h-[95vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between p-6 border-b flex-shrink-0">
          <h3 className="text-xl font-semibold">Add Test Image</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="flex flex-1 overflow-hidden">
          <StepNavigation
            currentStep={step}
            completedParameters={completedParameters}
            totalParameters={totalParameters}
          />

          <ScrollArea className="flex-1">
            <div className="p-6">
              {step === 1 && (
                <ImageSetupStep
                  imageUrl={imageUrl}
                  imageName={imageName}
                  notes={notes}
                  imageLoaded={imageLoaded}
                  onImageUrlChange={setImageUrl}
                  onImageNameChange={setImageName}
                  onNotesChange={setNotes}
                  onImageLoad={handleImageLoad}
                  onNext={() => setStep(2)}
                />
              )}

              {step === 2 && (
                <GroundTruthStep
                  groundTruth={groundTruth}
                  completedParameters={completedParameters}
                  totalParameters={totalParameters}
                  onParameterChange={handleParameterChange}
                  onBack={() => setStep(1)}
                  onNext={() => setStep(3)}
                  isComplete={isComplete()}
                />
              )}

              {step === 3 && (
                <ReviewStep
                  imageName={imageName}
                  imageUrl={imageUrl}
                  notes={notes}
                  groundTruth={groundTruth}
                  onBack={() => setStep(2)}
                  onSubmit={handleSubmit}
                />
              )}
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
};
