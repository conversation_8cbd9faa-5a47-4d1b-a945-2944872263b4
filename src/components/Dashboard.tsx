
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Plus, Play, BarChart3, Clock, Users, Target, Zap } from 'lucide-react';
import { AddImageModal } from './AddImageModal';
import { DatasetManager } from './DatasetManager';
import { TestExecution } from './TestExecution';
import { ResultsAnalysis } from './ResultsAnalysis';
import { SoloTests } from './SoloTests';
import type { TestDataset, TestExecution as TestExecutionType } from '@/types/ergonomic';

interface DashboardProps {
  datasets: TestDataset[];
  executions: TestExecutionType[];
  onAddDataset: (dataset: TestDataset) => void;
  onUpdateDataset: (dataset: TestDataset) => void;
  onRunTest: (execution: TestExecutionType) => void;
}

export const Dashboard: React.FC<DashboardProps> = ({
  datasets,
  executions,
  onAddDataset,
  onUpdateDataset,
  onRunTest
}) => {
  const [activeView, setActiveView] = useState<'overview' | 'datasets' | 'testing' | 'results' | 'solo'>('overview');
  const [selectedDataset, setSelectedDataset] = useState<TestDataset | null>(null);

  const totalImages = datasets.reduce((sum, dataset) => sum + dataset.images.length, 0);
  const recentExecution = executions[0];
  const avgPassRate = executions.length > 0 
    ? executions.reduce((sum, exec) => sum + exec.summary.overallPassRate, 0) / executions.length 
    : 0;

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b pb-6">
        <h1 className="text-3xl font-bold text-gray-900">AI Ergonomic Assessment Evaluation</h1>
        <p className="text-gray-600 mt-2">Test and validate AI ergonomic assessment accuracy against expert ground truth data</p>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Datasets</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{datasets.length}</div>
            <p className="text-xs text-muted-foreground">Active test suites</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Test Images</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalImages}</div>
            <p className="text-xs text-muted-foreground">Ground truth assessments</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Test Executions</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{executions.length}</div>
            <p className="text-xs text-muted-foreground">Total validation runs</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Pass Rate</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgPassRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">AI accuracy score</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveView('solo')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Solo Tests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Run individual API tests with custom images and view detailed results</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveView('datasets')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Manage Datasets
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Add new images and ground truth assessments to your test datasets</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveView('testing')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Run AI Tests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Execute evaluation tests against your AI ergonomic assessment API</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveView('results')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              View Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Analyze test results and track AI performance over time</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      {recentExecution && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Test Execution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Dataset: {datasets.find(d => d.id === recentExecution.datasetId)?.name}</p>
                <p className="text-sm text-gray-600">Executed: {new Date(recentExecution.timestamp).toLocaleString()}</p>
              </div>
              <Badge variant={recentExecution.summary.overallPassRate >= 80 ? "default" : "secondary"}>
                {recentExecution.summary.overallPassRate.toFixed(1)}% Pass Rate
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex space-x-8">
              <button
                onClick={() => setActiveView('overview')}
                className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                  activeView === 'overview' 
                    ? 'border-blue-500 text-gray-900' 
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveView('solo')}
                className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                  activeView === 'solo' 
                    ? 'border-blue-500 text-gray-900' 
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Solo Tests
              </button>
              <button
                onClick={() => setActiveView('datasets')}
                className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                  activeView === 'datasets' 
                    ? 'border-blue-500 text-gray-900' 
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Datasets
              </button>
              <button
                onClick={() => setActiveView('testing')}
                className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                  activeView === 'testing' 
                    ? 'border-blue-500 text-gray-900' 
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Testing
              </button>
              <button
                onClick={() => setActiveView('results')}
                className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                  activeView === 'results' 
                    ? 'border-blue-500 text-gray-900' 
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Results
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {activeView === 'overview' && renderOverview()}
          {activeView === 'solo' && <SoloTests />}
          {activeView === 'datasets' && (
            <DatasetManager 
              datasets={datasets} 
              onAddDataset={onAddDataset}
              onUpdateDataset={onUpdateDataset}
              onSelectDataset={setSelectedDataset}
            />
          )}
          {activeView === 'testing' && (
            <TestExecution 
              datasets={datasets}
              onRunTest={onRunTest}
            />
          )}
          {activeView === 'results' && (
            <ResultsAnalysis 
              executions={executions}
              datasets={datasets}
            />
          )}
        </div>
      </main>
    </div>
  );
};
