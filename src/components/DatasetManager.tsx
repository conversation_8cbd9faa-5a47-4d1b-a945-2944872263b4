
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus, Image, Calendar, Settings } from 'lucide-react';
import { AddImageModal } from './AddImageModal';
import type { TestDataset, TestImage } from '@/types/ergonomic';

interface DatasetManagerProps {
  datasets: TestDataset[];
  onAddDataset: (dataset: TestDataset) => void;
  onUpdateDataset: (dataset: TestDataset) => void;
  onSelectDataset: (dataset: TestDataset) => void;
}

export const DatasetManager: React.FC<DatasetManagerProps> = ({
  datasets,
  onAddDataset,
  onUpdateDataset,
  onSelectDataset
}) => {
  const [selectedDataset, setSelectedDataset] = useState<TestDataset | null>(null);
  const [showAddImage, setShowAddImage] = useState(false);
  const [showCreateDataset, setShowCreateDataset] = useState(false);

  const handleAddImage = (image: TestImage) => {
    if (selectedDataset) {
      const updatedDataset = {
        ...selectedDataset,
        images: [...selectedDataset.images, image],
        updatedAt: new Date().toISOString()
      };
      onUpdateDataset(updatedDataset);
      setSelectedDataset(updatedDataset);
    }
    setShowAddImage(false);
  };

  const handleCreateDataset = (name: string, description: string) => {
    const newDataset: TestDataset = {
      id: Date.now().toString(),
      name,
      images: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    onAddDataset(newDataset);
    setSelectedDataset(newDataset);
    setShowCreateDataset(false);
  };

  if (selectedDataset) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Button 
              variant="ghost" 
              onClick={() => setSelectedDataset(null)}
              className="mb-2"
            >
              ← Back to Datasets
            </Button>
            <h2 className="text-2xl font-bold text-gray-900">{selectedDataset.name}</h2>
            <p className="text-gray-600">Manage test images and ground truth assessments</p>
          </div>
          <Button onClick={() => setShowAddImage(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Image
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {selectedDataset.images.map((image) => (
            <Card key={image.id} className="overflow-hidden">
              <div className="aspect-video bg-gray-100 flex items-center justify-center">
                <img 
                  src={image.url} 
                  alt={image.name}
                  className="max-w-full max-h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.parentElement!.innerHTML = '<div class="text-gray-400">Image not available</div>';
                  }}
                />
              </div>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">{image.name}</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  <Badge variant="outline">22 Parameters Set</Badge>
                  {image.metadata?.addedAt && (
                    <p className="text-xs text-gray-500">
                      Added: {new Date(image.metadata.addedAt).toLocaleDateString()}
                    </p>
                  )}
                  {image.metadata?.notes && (
                    <p className="text-xs text-gray-600 line-clamp-2">{image.metadata.notes}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {selectedDataset.images.length === 0 && (
          <Card className="py-12">
            <CardContent className="text-center">
              <Image className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No images yet</h3>
              <p className="text-gray-600 mb-4">Start building your test dataset by adding images with ground truth assessments</p>
              <Button onClick={() => setShowAddImage(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Image
              </Button>
            </CardContent>
          </Card>
        )}

        {showAddImage && (
          <AddImageModal
            onAddImage={handleAddImage}
            onClose={() => setShowAddImage(false)}
          />
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Test Datasets</h2>
          <p className="text-gray-600">Manage your ergonomic assessment test datasets</p>
        </div>
        <Button onClick={() => setShowCreateDataset(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Dataset
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {datasets.map((dataset) => (
          <Card 
            key={dataset.id} 
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => setSelectedDataset(dataset)}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {dataset.name}
                <Settings className="h-4 w-4 text-gray-400" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Images</span>
                  <Badge variant="secondary">{dataset.images.length}</Badge>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Calendar className="h-3 w-3" />
                  Created {new Date(dataset.createdAt).toLocaleDateString()}
                </div>
                {dataset.updatedAt !== dataset.createdAt && (
                  <div className="text-xs text-gray-400">
                    Updated {new Date(dataset.updatedAt).toLocaleDateString()}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {datasets.length === 0 && (
        <Card className="py-12">
          <CardContent className="text-center">
            <Image className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No datasets yet</h3>
            <p className="text-gray-600 mb-4">Create your first test dataset to start evaluating AI ergonomic assessments</p>
            <Button onClick={() => setShowCreateDataset(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Dataset
            </Button>
          </CardContent>
        </Card>
      )}

      {showCreateDataset && (
        <CreateDatasetModal
          onCreateDataset={handleCreateDataset}
          onClose={() => setShowCreateDataset(false)}
        />
      )}
    </div>
  );
};

const CreateDatasetModal: React.FC<{
  onCreateDataset: (name: string, description: string) => void;
  onClose: () => void;
}> = ({ onCreateDataset, onClose }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      onCreateDataset(name.trim(), description.trim());
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Create New Dataset</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Dataset Name *
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Office Ergonomics v1.0"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
              placeholder="Optional description..."
            />
          </div>
          <div className="flex gap-3">
            <Button type="submit" className="flex-1">
              Create Dataset
            </Button>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
