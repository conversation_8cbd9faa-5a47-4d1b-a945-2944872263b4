
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BarChart3, TrendingUp, Download, Calendar, Target } from 'lucide-react';
import type { TestExecution, TestDataset } from '@/types/ergonomic';

interface ResultsAnalysisProps {
  executions: TestExecution[];
  datasets: TestDataset[];
}

export const ResultsAnalysis: React.FC<ResultsAnalysisProps> = ({ executions, datasets }) => {
  const [selectedExecution, setSelectedExecution] = useState<TestExecution | null>(
    executions.length > 0 ? executions[0] : null
  );

  const getDatasetName = (datasetId: string) => {
    return datasets.find(d => d.id === datasetId)?.name || 'Unknown Dataset';
  };

  const exportResults = (execution: TestExecution) => {
    const data = {
      execution: {
        id: execution.id,
        timestamp: execution.timestamp,
        dataset: getDatasetName(execution.datasetId),
        summary: execution.summary
      },
      results: execution.results
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `test-results-${execution.id}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getParameterStats = (execution: TestExecution) => {
    const paramStats: { [param: string]: { passed: number; total: number } } = {};
    
    execution.results.forEach(result => {
      if (!paramStats[result.parameter]) {
        paramStats[result.parameter] = { passed: 0, total: 0 };
      }
      paramStats[result.parameter].total++;
      if (result.passed) {
        paramStats[result.parameter].passed++;
      }
    });

    return Object.entries(paramStats)
      .map(([param, stats]) => ({
        parameter: param,
        passRate: (stats.passed / stats.total) * 100,
        passed: stats.passed,
        total: stats.total
      }))
      .sort((a, b) => b.passRate - a.passRate);
  };

  const getImageStats = (execution: TestExecution) => {
    const imageStats: { [imageId: string]: { passed: number; total: number } } = {};
    
    execution.results.forEach(result => {
      if (!imageStats[result.imageId]) {
        imageStats[result.imageId] = { passed: 0, total: 0 };
      }
      imageStats[result.imageId].total++;
      if (result.passed) {
        imageStats[result.imageId].passed++;
      }
    });

    return Object.entries(imageStats)
      .map(([imageId, stats]) => ({
        imageId,
        passRate: (stats.passed / stats.total) * 100,
        passed: stats.passed,
        total: stats.total
      }))
      .sort((a, b) => b.passRate - a.passRate);
  };

  if (executions.length === 0) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Results Analysis</h2>
          <p className="text-gray-600">Analyze AI performance and track improvements over time</p>
        </div>

        <Card className="py-12">
          <CardContent className="text-center">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No test results yet</h3>
            <p className="text-gray-600 mb-4">Run your first AI evaluation test to see results and analytics here</p>
            <Button variant="outline">Go to Testing</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Results Analysis</h2>
          <p className="text-gray-600">Analyze AI performance and track improvements over time</p>
        </div>
        {selectedExecution && (
          <Button 
            onClick={() => exportResults(selectedExecution)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export Results
          </Button>
        )}
      </div>

      {/* Execution Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Test Executions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {executions.map((execution) => (
              <div
                key={execution.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedExecution?.id === execution.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedExecution(execution)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{getDatasetName(execution.datasetId)}</h4>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Calendar className="h-3 w-3" />
                      {new Date(execution.timestamp).toLocaleString()}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge 
                      variant={execution.summary.overallPassRate >= 80 ? "default" : "secondary"}
                      className="mb-1"
                    >
                      {execution.summary.overallPassRate.toFixed(1)}% Pass Rate
                    </Badge>
                    <div className="text-sm text-gray-600">
                      {execution.summary.totalImages} images
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {selectedExecution && (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Overall Pass Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {selectedExecution.summary.overallPassRate.toFixed(1)}%
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Images</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {selectedExecution.summary.totalImages}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Parameters Tested</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {selectedExecution.summary.totalParameters}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Test Timestamp</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm">
                  {new Date(selectedExecution.timestamp).toLocaleDateString()}
                </div>
                <div className="text-xs text-gray-600">
                  {new Date(selectedExecution.timestamp).toLocaleTimeString()}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Parameter Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Parameter Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {getParameterStats(selectedExecution).map((stat) => (
                  <div key={stat.parameter} className="flex items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">{stat.parameter}</span>
                        <span className="text-sm text-gray-600">
                          {stat.passed}/{stat.total} ({stat.passRate.toFixed(1)}%)
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            stat.passRate >= 80 ? 'bg-green-500' : 
                            stat.passRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${stat.passRate}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Image Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Image Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getImageStats(selectedExecution).map((stat, index) => (
                  <div key={stat.imageId} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Image {index + 1}</span>
                      <Badge 
                        variant={stat.passRate >= 80 ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {stat.passRate.toFixed(0)}%
                      </Badge>
                    </div>
                    <div className="text-xs text-gray-600">
                      {stat.passed} of {stat.total} parameters passed
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1 mt-2">
                      <div
                        className={`h-1 rounded-full ${
                          stat.passRate >= 80 ? 'bg-green-500' : 
                          stat.passRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${stat.passRate}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Historical Trends */}
      {executions.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Historical Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                Showing performance trends across {executions.length} test executions
              </div>
              
              <div className="space-y-2">
                {executions.map((execution, index) => (
                  <div key={execution.id} className="flex items-center gap-4">
                    <div className="w-24 text-xs text-gray-500">
                      {new Date(execution.timestamp).toLocaleDateString()}
                    </div>
                    <div className="flex-1">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: `${execution.summary.overallPassRate}%` }}
                        />
                      </div>
                    </div>
                    <div className="w-16 text-sm text-right">
                      {execution.summary.overallPassRate.toFixed(1)}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
