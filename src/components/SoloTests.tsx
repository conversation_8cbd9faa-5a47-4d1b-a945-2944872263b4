
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Play, Loader2 } from 'lucide-react';
import { APISelector } from './solo/APISelector';
import { ImageUpload } from './solo/ImageUpload';
import { ResultsDisplay } from './solo/ResultsDisplay';
import { SoloTestService } from '@/services/soloTestService';
import type { APIEndpoint, SoloTestResult } from '@/types/api';
import apisConfig from '@/config/apis.json';

export const SoloTests: React.FC = () => {
  const [apis] = useState<APIEndpoint[]>(apisConfig.apis as APIEndpoint[]);
  const [selectedApi, setSelectedApi] = useState<APIEndpoint | null>(null);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [isRunning, setIsRunning] = useState(false);
  const [result, setResult] = useState<SoloTestResult | null>(null);

  const canRunTest = selectedApi && imageUrl && !isRunning;

  const handleRunTest = async () => {
    if (!selectedApi || !imageUrl) return;

    setIsRunning(true);
    setResult(null);

    try {
      const testResult = await SoloTestService.executeTest(selectedApi, imageUrl);
      setResult(testResult);
    } catch (error) {
      setResult({
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString(),
        responseTime: 0
      });
    } finally {
      setIsRunning(false);
    }
  };

  const handleImageSelect = (url: string) => {
    setImageUrl(url);
    setResult(null); // Clear previous results when new image is selected
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* API Selection Sidebar */}
      <APISelector
        apis={apis}
        selectedApi={selectedApi}
        onSelectApi={setSelectedApi}
      />

      {/* Main Content */}
      <div className="flex-1 p-6">
        <div className="max-w-none mx-auto space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Solo Tests</h1>
            <p className="text-gray-600 mt-2">
              Run individual API tests with custom images and view detailed results
            </p>
          </div>

          {/* Test Configuration */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-3 3xl:grid-cols-4 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Selected API</CardTitle>
              </CardHeader>
              <CardContent>
                {selectedApi ? (
                  <div className="space-y-2">
                    <h3 className="font-medium">{selectedApi.name}</h3>
                    <p className="text-sm text-gray-600">{selectedApi.description}</p>
                    <div className="flex gap-2">
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {selectedApi.method}
                      </span>
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        {selectedApi.category}
                      </span>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500">Select an API from the sidebar to get started</p>
                )}
              </CardContent>
            </Card>

            <ImageUpload onImageSelect={handleImageSelect} disabled={isRunning} />
          </div>

          {/* Image Preview */}
          {imageUrl && (
            <Card>
              <CardHeader>
                <CardTitle>Test Image</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-4 bg-gray-50">
                  <img
                    src={imageUrl}
                    alt="Test image"
                    className="max-w-full max-h-64 object-contain mx-auto rounded"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Run Test Button */}
          {selectedApi && imageUrl && (
            <div className="flex justify-center">
              <Button
                onClick={handleRunTest}
                disabled={!canRunTest}
                size="lg"
                className="px-8"
              >
                {isRunning ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Running Test...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Run Test
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Results */}
          {result && (
            <ResultsDisplay
              result={result}
              apiName={selectedApi?.name || 'Unknown API'}
            />
          )}
        </div>
      </div>
    </div>
  );
};
