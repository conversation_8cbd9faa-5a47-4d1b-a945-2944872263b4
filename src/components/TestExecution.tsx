
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, Settings, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import type { TestDataset, APIConfiguration, TestExecution as TestExecutionType } from '@/types/ergonomic';

interface TestExecutionProps {
  datasets: TestDataset[];
  onRunTest: (execution: TestExecutionType) => void;
}

export const TestExecution: React.FC<TestExecutionProps> = ({ datasets, onRunTest }) => {
  const [selectedDataset, setSelectedDataset] = useState<TestDataset | null>(null);
  const [apiConfig, setApiConfig] = useState<APIConfiguration>({
    endpoint: 'https://api.example.com/ergonomic-assessment',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    authType: 'none',
    timeout: 30000
  });
  const [isRunning, setIsRunning] = useState(false);
  const [currentProgress, setCurrentProgress] = useState({ current: 0, total: 0 });
  const [testResults, setTestResults] = useState<any[]>([]);

  const handleRunTest = async () => {
    if (!selectedDataset) return;

    setIsRunning(true);
    setCurrentProgress({ current: 0, total: selectedDataset.images.length });
    
    // Simulate test execution
    const results: any[] = [];
    
    for (let i = 0; i < selectedDataset.images.length; i++) {
      const image = selectedDataset.images[i];
      setCurrentProgress({ current: i + 1, total: selectedDataset.images.length });
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock test results
      const mockResults = generateMockResults(image.id, image.groundTruth);
      results.push(...mockResults);
    }

    const summary = calculateSummary(results, selectedDataset);
    
    const execution: TestExecutionType = {
      id: Date.now().toString(),
      datasetId: selectedDataset.id,
      apiConfig,
      results,
      summary,
      timestamp: new Date().toISOString()
    };

    setTestResults(results);
    setIsRunning(false);
    onRunTest(execution);
  };

  const generateMockResults = (imageId: string, groundTruth: any) => {
    const parameters = Object.keys(groundTruth);
    return parameters.map(param => ({
      imageId,
      parameter: param,
      expected: groundTruth[param],
      actual: Math.random() > 0.3 ? groundTruth[param] : getRandomValue(param),
      passed: Math.random() > 0.2,
      confidence: Math.random() * 0.4 + 0.6
    }));
  };

  const getRandomValue = (param: string) => {
    const booleanParams = ['properWorkstation', 'properSideProfileImage', 'legsVisible'];
    if (booleanParams.includes(param)) {
      return Math.random() > 0.5;
    }
    return 'random_value';
  };

  const calculateSummary = (results: any[], dataset: TestDataset) => {
    const totalParams = results.length;
    const passedParams = results.filter(r => r.passed).length;
    
    return {
      totalImages: dataset.images.length,
      totalParameters: totalParams,
      overallPassRate: (passedParams / totalParams) * 100,
      imagePassRates: {},
      parameterPassRates: {}
    };
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">AI Test Execution</h2>
        <p className="text-gray-600">Configure and run AI ergonomic assessment validation tests</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Dataset Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Select Test Dataset</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {datasets.map((dataset) => (
                <div
                  key={dataset.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedDataset?.id === dataset.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedDataset(dataset)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{dataset.name}</h4>
                      <p className="text-sm text-gray-600">{dataset.images.length} images</p>
                    </div>
                    <Badge variant="outline">{dataset.images.length}</Badge>
                  </div>
                </div>
              ))}
              
              {datasets.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                  <p>No datasets available. Create a dataset first.</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* API Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              API Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Endpoint URL
                </label>
                <input
                  type="url"
                  value={apiConfig.endpoint}
                  onChange={(e) => setApiConfig(prev => ({ ...prev, endpoint: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://api.example.com/assess"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Method
                </label>
                <select
                  value={apiConfig.method}
                  onChange={(e) => setApiConfig(prev => ({ ...prev, method: e.target.value as 'POST' | 'PUT' }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="POST">POST</option>
                  <option value="PUT">PUT</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Authentication
                </label>
                <select
                  value={apiConfig.authType}
                  onChange={(e) => setApiConfig(prev => ({ 
                    ...prev, 
                    authType: e.target.value as 'none' | 'bearer' | 'apiKey' 
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="none">None</option>
                  <option value="bearer">Bearer Token</option>
                  <option value="apiKey">API Key</option>
                </select>
              </div>

              {apiConfig.authType !== 'none' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {apiConfig.authType === 'bearer' ? 'Bearer Token' : 'API Key'}
                  </label>
                  <input
                    type="password"
                    value={apiConfig.authToken || ''}
                    onChange={(e) => setApiConfig(prev => ({ ...prev, authToken: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter your token/key"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Timeout (ms)
                </label>
                <input
                  type="number"
                  value={apiConfig.timeout}
                  onChange={(e) => setApiConfig(prev => ({ ...prev, timeout: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="1000"
                  step="1000"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Execution */}
      {selectedDataset && (
        <Card>
          <CardHeader>
            <CardTitle>Test Execution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Ready to test: {selectedDataset.name}</h4>
                  <p className="text-sm text-gray-600">
                    {selectedDataset.images.length} images × 22 parameters = {selectedDataset.images.length * 22} total assessments
                  </p>
                </div>
                <Button
                  onClick={handleRunTest}
                  disabled={isRunning || !apiConfig.endpoint}
                  className="flex items-center gap-2"
                >
                  {isRunning ? (
                    <>
                      <Clock className="h-4 w-4 animate-spin" />
                      Running...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4" />
                      Start Test
                    </>
                  )}
                </Button>
              </div>

              {isRunning && (
                <div>
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Progress: {currentProgress.current} / {currentProgress.total} images</span>
                    <span>{Math.round((currentProgress.current / currentProgress.total) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(currentProgress.current / currentProgress.total) * 100}%` }}
                    />
                  </div>
                </div>
              )}

              {testResults.length > 0 && (
                <div className="border-t pt-4">
                  <div className="flex items-center gap-2 text-green-600">
                    <CheckCircle className="h-5 w-5" />
                    <span className="font-medium">Test completed successfully!</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Results are now available in the Results section.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
