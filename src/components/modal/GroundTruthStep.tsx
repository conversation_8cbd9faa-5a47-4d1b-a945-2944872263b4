
import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check } from 'lucide-react';
import { ParameterInput } from './ParameterInput';
import type { ParameterDefinitions } from '@/types/ergonomic';

interface GroundTruthStepProps {
  groundTruth: Partial<ParameterDefinitions>;
  completedParameters: number;
  totalParameters: number;
  onParameterChange: (key: string, value: any) => void;
  onBack: () => void;
  onNext: () => void;
  isComplete: boolean;
}

const parameterConfig = {
  properWorkstation: { type: 'boolean', label: 'Proper Workstation' },
  properSideProfileImage: { type: 'boolean', label: 'Proper Side Profile Image' },
  facingSide: { 
    type: 'select', 
    label: 'Facing Side', 
    options: ['left', 'right', 'front', 'back'] 
  },
  legsVisible: { type: 'boolean', label: 'Legs Visible' },
  posture: { 
    type: 'select', 
    label: 'Posture', 
    options: ['Sitting back', 'perched forward', 'slouching', 'leaning forward'] 
  },
  sittingHeight: { 
    type: 'select', 
    label: 'Sitting Height', 
    options: ['hips lower than knees', 'hips inline with knees', 'Hips higher than knees legs grounded', 'Hips higher than knees legs hanging'] 
  },
  deskHeight: { 
    type: 'select', 
    label: 'Desk Height', 
    options: ['inline with elbow', 'above elbow', 'below elbow'] 
  },
  armrest: { 
    type: 'select', 
    label: 'Armrest', 
    options: ['above elbow', 'below elbow', 'inline with elbow', 'no armrests'] 
  },
  seatpan: { 
    type: 'select', 
    label: 'Seatpan', 
    options: ['2 finger spacing', 'More than 2 finger spacing', 'less than 2 finger spacing'] 
  },
  numberOfMonitors: { type: 'number', label: 'Number of Monitors' },
  numberOfLaptops: { type: 'number', label: 'Number of Laptops' },
  keyboard: { type: 'number', label: 'Keyboard Count' },
  mouse: { type: 'number', label: 'Mouse Count' },
  laptopStatus: { 
    type: 'select', 
    label: 'Laptop Status', 
    options: ['open', 'closed'] 
  },
  laptopUsage: { 
    type: 'select', 
    label: 'Laptop Usage', 
    options: ['open', 'docked'] 
  },
  screenDistance: { 
    type: 'select', 
    label: 'Screen Distance', 
    options: ['At arms length', 'Less than arms length', 'More than arms length'] 
  },
  laptopDistance: { 
    type: 'select', 
    label: 'Laptop Distance', 
    options: ['At arms length', 'Less than arms length', 'More than arms length'] 
  },
  screenHeight: { 
    type: 'select', 
    label: 'Screen Height', 
    options: ['below eye level', 'at eye level', 'above eye level'] 
  },
  laptopHeight: { 
    type: 'select', 
    label: 'Laptop Height', 
    options: ['below eye level', 'at eye level', 'above eye level'] 
  },
  keyboardPlacement: { 
    type: 'select', 
    label: 'Keyboard Placement', 
    options: ['Palm distance from edge', 'Extended and reaching forward', 'Edge of the table'] 
  },
  mousePlacement: { 
    type: 'select', 
    label: 'Mouse Placement', 
    options: ['Palm distance from edge', 'Extended and reaching forward', 'Edge of the table'] 
  }
} as const;

export const GroundTruthStep: React.FC<GroundTruthStepProps> = ({
  groundTruth,
  completedParameters,
  totalParameters,
  onParameterChange,
  onBack,
  onNext,
  isComplete
}) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-medium">Ground Truth Parameters</h4>
        <Badge variant={isComplete ? "default" : "secondary"}>
          {completedParameters}/{totalParameters} Complete
        </Badge>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {Object.entries(parameterConfig).map(([key, config]) => {
          const isSet = groundTruth.hasOwnProperty(key);
          
          return (
            <div key={key} className={`p-4 border rounded-lg ${isSet ? 'border-green-200 bg-green-50' : 'border-gray-200'}`}>
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-gray-700">
                  {config.label}
                </label>
                {isSet && <Check className="h-4 w-4 text-green-600" />}
              </div>
              <ParameterInput
                parameterKey={key}
                config={config}
                value={groundTruth[key as keyof ParameterDefinitions]}
                onChange={onParameterChange}
              />
            </div>
          );
        })}
      </div>

      <div className="flex gap-3 pt-4 border-t">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button 
          onClick={onNext} 
          disabled={!isComplete}
          className="flex-1"
        >
          Review & Save
        </Button>
      </div>
    </div>
  );
};
