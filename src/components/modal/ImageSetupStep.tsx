
import React from 'react';
import { Button } from '@/components/ui/button';

interface ImageSetupStepProps {
  imageUrl: string;
  imageName: string;
  notes: string;
  imageLoaded: boolean;
  onImageUrlChange: (url: string) => void;
  onImageNameChange: (name: string) => void;
  onNotesChange: (notes: string) => void;
  onImageLoad: () => void;
  onNext: () => void;
}

export const ImageSetupStep: React.FC<ImageSetupStepProps> = ({
  imageUrl,
  imageName,
  notes,
  imageLoaded,
  onImageUrlChange,
  onImageNameChange,
  onNotesChange,
  onImageLoad,
  onNext
}) => {
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-medium mb-4">Image Information</h4>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Image URL *
            </label>
            <input
              type="url"
              value={imageUrl}
              onChange={(e) => onImageUrlChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="https://example.com/image.jpg"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Image Name
            </label>
            <input
              type="text"
              value={imageName}
              onChange={(e) => onImageNameChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Descriptive name for this image"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes (Optional)
            </label>
            <textarea
              value={notes}
              onChange={(e) => onNotesChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
              placeholder="Any relevant notes about this image..."
            />
          </div>
        </div>
      </div>

      {imageUrl && (
        <div>
          <h5 className="text-sm font-medium text-gray-700 mb-2">Preview</h5>
          <div className="border rounded-lg p-4 bg-gray-50">
            <img
              src={imageUrl}
              alt="Preview"
              onLoad={onImageLoad}
              onError={() => {}}
              className="max-w-full max-h-64 object-contain mx-auto"
            />
          </div>
        </div>
      )}

      <div className="flex gap-3">
        <Button 
          onClick={onNext} 
          disabled={!imageUrl || !imageLoaded}
          className="flex-1"
        >
          Next: Ground Truth Parameters
        </Button>
      </div>
    </div>
  );
};
