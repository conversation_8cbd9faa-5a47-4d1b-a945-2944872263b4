
import React from 'react';

interface ParameterConfig {
  type: 'boolean' | 'number' | 'select';
  label: string;
  options?: readonly string[];
}

interface ParameterInputProps {
  parameterKey: string;
  config: ParameterConfig;
  value: any;
  onChange: (key: string, value: any) => void;
}

export const ParameterInput: React.FC<ParameterInputProps> = ({
  parameterKey,
  config,
  value,
  onChange
}) => {
  if (config.type === 'boolean') {
    return (
      <div className="flex items-center gap-3">
        <button
          type="button"
          onClick={() => onChange(parameterKey, true)}
          className={`px-3 py-1 rounded text-sm ${
            value === true ? 'bg-green-100 text-green-800 border border-green-300' : 'bg-gray-100 text-gray-600'
          }`}
        >
          Yes
        </button>
        <button
          type="button"
          onClick={() => onChange(parameterKey, false)}
          className={`px-3 py-1 rounded text-sm ${
            value === false ? 'bg-red-100 text-red-800 border border-red-300' : 'bg-gray-100 text-gray-600'
          }`}
        >
          No
        </button>
      </div>
    );
  }

  if (config.type === 'number') {
    return (
      <input
        type="number"
        min="0"
        value={typeof value === 'number' ? value : ''}
        onChange={(e) => onChange(parameterKey, parseInt(e.target.value) || 0)}
        className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
      />
    );
  }

  if (config.type === 'select') {
    return (
      <select
        value={typeof value === 'string' ? value : ''}
        onChange={(e) => onChange(parameterKey, e.target.value)}
        className="px-2 py-1 border border-gray-300 rounded text-sm min-w-40"
      >
        <option value="">Select...</option>
        {config.options?.map((option: string) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </select>
    );
  }

  return null;
};
