
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Upload } from 'lucide-react';
import type { ParameterDefinitions } from '@/types/ergonomic';

interface ReviewStepProps {
  imageName: string;
  imageUrl: string;
  notes: string;
  groundTruth: Partial<ParameterDefinitions>;
  onBack: () => void;
  onSubmit: () => void;
}

const parameterConfig = {
  properWorkstation: { label: 'Proper Workstation' },
  properSideProfileImage: { label: 'Proper Side Profile Image' },
  facingSide: { label: 'Facing Side' },
  legsVisible: { label: 'Legs Visible' },
  posture: { label: 'Posture' },
  sittingHeight: { label: 'Sitting Height' },
  deskHeight: { label: 'Desk Height' },
  armrest: { label: 'Armrest' },
  seatpan: { label: 'Seatpan' },
  numberOfMonitors: { label: 'Number of Monitors' },
  numberOfLaptops: { label: 'Number of Laptops' },
  keyboard: { label: 'Keyboard Count' },
  mouse: { label: 'Mouse Count' },
  laptopStatus: { label: 'Laptop Status' },
  laptopUsage: { label: 'Laptop Usage' },
  screenDistance: { label: 'Screen Distance' },
  laptopDistance: { label: 'Laptop Distance' },
  screenHeight: { label: 'Screen Height' },
  laptopHeight: { label: 'Laptop Height' },
  keyboardPlacement: { label: 'Keyboard Placement' },
  mousePlacement: { label: 'Mouse Placement' }
} as const;

export const ReviewStep: React.FC<ReviewStepProps> = ({
  imageName,
  imageUrl,
  notes,
  groundTruth,
  onBack,
  onSubmit
}) => {
  return (
    <div className="space-y-6">
      <h4 className="text-lg font-medium">Review & Save</h4>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h5 className="font-medium mb-3">Image Information</h5>
          <div className="space-y-2 text-sm">
            <div><span className="font-medium">Name:</span> {imageName}</div>
            <div><span className="font-medium">URL:</span> {imageUrl}</div>
            {notes && <div><span className="font-medium">Notes:</span> {notes}</div>}
          </div>
          
          <div className="mt-4">
            <img
              src={imageUrl}
              alt={imageName}
              className="max-w-full max-h-48 object-contain border rounded"
            />
          </div>
        </div>

        <div>
          <h5 className="font-medium mb-3">Ground Truth Summary</h5>
          <ScrollArea className="h-64">
            <div className="text-sm space-y-1">
              {Object.entries(groundTruth).map(([key, value]) => (
                <div key={key} className="flex justify-between">
                  <span className="text-gray-600">{parameterConfig[key as keyof typeof parameterConfig].label}:</span>
                  <span className="font-medium">{value?.toString()}</span>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>

      <div className="flex gap-3 pt-4 border-t">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={onSubmit} className="flex-1">
          <Upload className="h-4 w-4 mr-2" />
          Save Image
        </Button>
      </div>
    </div>
  );
};
