
import React from 'react';
import { Check } from 'lucide-react';

interface StepNavigationProps {
  currentStep: number;
  completedParameters: number;
  totalParameters: number;
}

export const StepNavigation: React.FC<StepNavigationProps> = ({
  currentStep,
  completedParameters,
  totalParameters
}) => {
  return (
    <div className="w-64 bg-gray-50 p-6 border-r flex-shrink-0">
      <div className="space-y-4">
        <div className={`flex items-center gap-3 ${currentStep >= 1 ? 'text-blue-600' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            currentStep > 1 ? 'bg-blue-600 text-white' : currentStep === 1 ? 'bg-blue-100 text-blue-600' : 'bg-gray-200'
          }`}>
            {currentStep > 1 ? <Check className="h-4 w-4" /> : '1'}
          </div>
          <span>Image Setup</span>
        </div>
        
        <div className={`flex items-center gap-3 ${currentStep >= 2 ? 'text-blue-600' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            currentStep > 2 ? 'bg-blue-600 text-white' : currentStep === 2 ? 'bg-blue-100 text-blue-600' : 'bg-gray-200'
          }`}>
            {currentStep > 2 ? <Check className="h-4 w-4" /> : '2'}
          </div>
          <span>Ground Truth</span>
        </div>
        
        <div className={`flex items-center gap-3 ${currentStep >= 3 ? 'text-blue-600' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            currentStep === 3 ? 'bg-blue-100 text-blue-600' : 'bg-gray-200'
          }`}>
            3
          </div>
          <span>Review & Save</span>
        </div>
      </div>

      {currentStep === 2 && (
        <div className="mt-6 p-3 bg-white rounded border">
          <div className="text-sm font-medium text-gray-700 mb-2">Progress</div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(completedParameters / totalParameters) * 100}%` }}
            />
          </div>
          <div className="text-xs text-gray-600 mt-1">
            {completedParameters} of {totalParameters} parameters
          </div>
        </div>
      )}
    </div>
  );
};
