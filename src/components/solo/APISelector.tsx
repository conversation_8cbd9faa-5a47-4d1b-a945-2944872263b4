
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { APIEndpoint } from '@/types/api';

interface APISelectorProps {
  apis: APIEndpoint[];
  selectedApi: APIEndpoint | null;
  onSelectApi: (api: APIEndpoint) => void;
}

export const APISelector: React.FC<APISelectorProps> = ({
  apis,
  selectedApi,
  onSelectApi
}) => {
  const groupedApis = apis.reduce((groups, api) => {
    const category = api.category || 'Other';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(api);
    return groups;
  }, {} as Record<string, APIEndpoint[]>);

  return (
    <div className="w-80 bg-gray-50 p-6 border-r overflow-y-auto">
      <h3 className="text-lg font-semibold mb-4">Available APIs</h3>
      
      <div className="space-y-4">
        {Object.entries(groupedApis).map(([category, categoryApis]) => (
          <div key={category}>
            <h4 className="text-sm font-medium text-gray-600 mb-2">{category}</h4>
            <div className="space-y-2">
              {categoryApis.map((api) => (
                <Card
                  key={api.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedApi?.id === api.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                  }`}
                  onClick={() => onSelectApi(api)}
                >
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">{api.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      <Badge variant="outline" className="text-xs">
                        {api.method}
                      </Badge>
                      {api.description && (
                        <p className="text-xs text-gray-600">{api.description}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
