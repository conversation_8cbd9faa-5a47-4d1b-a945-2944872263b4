
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { CheckCircle, XCircle, Clock, Image } from 'lucide-react';
import type { SoloTestResult } from '@/types/api';

interface ResultsDisplayProps {
  result: SoloTestResult;
  apiName: string;
}

export const ResultsDisplay: React.FC<ResultsDisplayProps> = ({
  result,
  apiName
}) => {
  // Check if this is an AIEC API result for specialized formatting
  const isAiecAPI = apiName.includes('Posture') || apiName.includes('Height') || apiName.includes('Desk');

  const formatAiecResults = (data: any): Array<{key: string, value: any}> => {
    const items: Array<{key: string, value: any}> = [];

    // Handle sitting posture API response
    if (data.posture_category) {
      items.push({ key: 'Posture Category', value: data.posture_category });
      if (data.posture_details) {
        items.push({ key: 'Spine Angle', value: `${data.posture_details.spine_angle?.toFixed(2)}°` });
        items.push({ key: 'Distance', value: `${data.posture_details.distance_cm?.toFixed(2)} cm` });
        items.push({ key: 'Hip Distance', value: `${data.posture_details.hip_distance_cm?.toFixed(2)} cm` });
        items.push({ key: 'Shoulder Distance', value: `${data.posture_details.shoulder_distance_cm?.toFixed(2)} cm` });
      }
    }

    // Handle sitting height API response
    if (data.outcome && data.analysis) {
      items.push({ key: 'Outcome', value: data.outcome });
      items.push({ key: 'Analysis', value: data.analysis });
      if (data.debug_info?.calculations) {
        const calc = data.debug_info.calculations;
        items.push({ key: 'Hip-Knee-Ankle Angle', value: `${calc.hip_knee_ankle_angle?.toFixed(2)}°` });
        items.push({ key: 'Hip Below Knee', value: calc.hip_below_knee ? 'Yes' : 'No' });
        items.push({ key: 'Ankle Between Hips & Knee', value: calc.ankle_between_hips_knee ? 'Yes' : 'No' });
      }
    }

    // Handle desk height API response
    if (data.desk_height) {
      items.push({ key: 'Desk Height', value: data.desk_height });
      items.push({ key: 'Analysis', value: data.analysis });
      if (data.debug_info?.calculations) {
        const calc = data.debug_info.calculations;
        items.push({ key: 'Shoulder-Elbow-Wrist Angle', value: `${calc.shoulder_elbow_wrist_angle?.toFixed(2)}°` });
        items.push({ key: 'Forearm Angle', value: `${calc.forearm_angle?.toFixed(2)}°` });
        items.push({ key: 'Wrist Position', value: calc.wrist_position });
      }
    }

    return items;
  };

  const formatResultsForTable = (result: SoloTestResult): Array<{key: string, value: any}> => {
    if (!result.success || !result.data) {
      return [
        { key: 'Status', value: 'Failed' },
        { key: 'Error', value: result.error || 'Unknown error' }
      ];
    }

    const formatValue = (value: any): string => {
      if (typeof value === 'object' && value !== null) {
        return JSON.stringify(value, null, 2);
      }
      return String(value);
    };

    const flatten = (obj: any, prefix = ''): Array<{key: string, value: any}> => {
      const items: Array<{key: string, value: any}> = [];

      for (const [key, value] of Object.entries(obj)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;

        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          items.push(...flatten(value, fullKey));
        } else {
          items.push({ key: fullKey, value: formatValue(value) });
        }
      }

      return items;
    };

    // Use specialized formatting for AIEC APIs, otherwise use generic flattening
    if (isAiecAPI) {
      return formatAiecResults(result.data);
    }

    return flatten(result.data);
  };

  const tableData = formatResultsForTable(result);

  return (
    <div className="space-y-6">
      {/* Status Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {result.success ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <XCircle className="h-5 w-5 text-red-600" />
            )}
            Test Results - {apiName}
            <Badge variant={result.success ? "default" : "destructive"}>
              {result.success ? 'Success' : 'Failed'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              {result.responseTime}ms
            </div>
            <div>
              Executed: {new Date(result.timestamp).toLocaleString()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* AIEC API Key Results */}
      {isAiecAPI && result.success && (
        <Card>
          <CardHeader>
            <CardTitle>Key Analysis Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {result.data.posture_category && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-900">Posture Category</h4>
                  <p className="text-2xl font-bold text-blue-700 capitalize">{result.data.posture_category}</p>
                </div>
              )}
              {result.data.outcome && (
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-900">Sitting Height</h4>
                  <p className="text-2xl font-bold text-green-700 capitalize">{result.data.outcome}</p>
                </div>
              )}
              {result.data.desk_height && (
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-purple-900">Desk Height</h4>
                  <p className="text-2xl font-bold text-purple-700 capitalize">{result.data.desk_height}</p>
                </div>
              )}
              {result.data.posture_details?.spine_angle && (
                <div className="bg-orange-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-orange-900">Spine Angle</h4>
                  <p className="text-2xl font-bold text-orange-700">{result.data.posture_details.spine_angle.toFixed(1)}°</p>
                </div>
              )}
              {result.data.debug_info?.calculations?.hip_knee_ankle_angle && (
                <div className="bg-indigo-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-indigo-900">Hip-Knee-Ankle Angle</h4>
                  <p className="text-2xl font-bold text-indigo-700">{result.data.debug_info.calculations.hip_knee_ankle_angle.toFixed(1)}°</p>
                </div>
              )}
              {result.data.debug_info?.calculations?.shoulder_elbow_wrist_angle && (
                <div className="bg-pink-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-pink-900">Shoulder-Elbow-Wrist Angle</h4>
                  <p className="text-2xl font-bold text-pink-700">{result.data.debug_info.calculations.shoulder_elbow_wrist_angle.toFixed(1)}°</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Annotated Image */}
      {result.annotatedImageUrl && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Image className="h-5 w-5" />
              Annotated Image
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 bg-gray-50">
              <img
                src={result.annotatedImageUrl}
                alt="Annotated result"
                className="max-w-full max-h-96 object-contain mx-auto rounded"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Table */}
      <Card>
        <CardHeader>
          <CardTitle>Response Data</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-1/3">Parameter</TableHead>
                  <TableHead>Value</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tableData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{item.key}</TableCell>
                    <TableCell>
                      <div className="max-w-md">
                        {typeof item.value === 'string' && item.value.length > 100 ? (
                          <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto whitespace-pre-wrap">
                            {item.value}
                          </pre>
                        ) : (
                          <span className="break-words">{item.value}</span>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
