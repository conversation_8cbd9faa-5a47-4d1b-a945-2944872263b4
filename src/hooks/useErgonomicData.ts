
import { useState, useEffect } from 'react';
import type { TestDataset, TestExecution } from '@/types/ergonomic';
import { persistenceService } from '@/services/persistenceService';

export const useErgonomicData = () => {
  const [datasets, setDatasets] = useState<TestDataset[]>([]);
  const [executions, setExecutions] = useState<TestExecution[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize database and load data
  useEffect(() => {
    const initializeData = async () => {
      try {
        setIsLoading(true);
        await persistenceService.initialize();
        
        const [loadedDatasets, loadedExecutions] = await Promise.all([
          persistenceService.getDatasets(),
          persistenceService.getExecutions()
        ]);

        setDatasets(loadedDatasets);
        setExecutions(loadedExecutions);
        setError(null);
      } catch (err) {
        console.error('Failed to initialize data:', err);
        setError('Failed to load data from storage');
        
        // Fallback to localStorage for backwards compatibility
        try {
          const savedDatasets = localStorage.getItem('ergonomic-datasets');
          const savedExecutions = localStorage.getItem('ergonomic-executions');

          if (savedDatasets) {
            setDatasets(JSON.parse(savedDatasets));
          }
          if (savedExecutions) {
            setExecutions(JSON.parse(savedExecutions));
          }
        } catch (fallbackErr) {
          console.error('Fallback to localStorage also failed:', fallbackErr);
        }
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, []);

  const addDataset = async (dataset: TestDataset) => {
    try {
      await persistenceService.saveDataset(dataset);
      setDatasets(prev => [...prev, dataset]);
    } catch (err) {
      console.error('Failed to save dataset:', err);
      setError('Failed to save dataset');
    }
  };

  const updateDataset = async (updatedDataset: TestDataset) => {
    try {
      await persistenceService.saveDataset(updatedDataset);
      setDatasets(prev => 
        prev.map(dataset => 
          dataset.id === updatedDataset.id ? updatedDataset : dataset
        )
      );
    } catch (err) {
      console.error('Failed to update dataset:', err);
      setError('Failed to update dataset');
    }
  };

  const deleteDataset = async (datasetId: string) => {
    try {
      await persistenceService.deleteDataset(datasetId);
      setDatasets(prev => prev.filter(dataset => dataset.id !== datasetId));
      
      // Also remove associated executions
      const datasetExecutions = executions.filter(exec => exec.datasetId === datasetId);
      for (const execution of datasetExecutions) {
        await persistenceService.deleteExecution(execution.id);
      }
      setExecutions(prev => prev.filter(exec => exec.datasetId !== datasetId));
    } catch (err) {
      console.error('Failed to delete dataset:', err);
      setError('Failed to delete dataset');
    }
  };

  const addExecution = async (execution: TestExecution) => {
    try {
      await persistenceService.saveExecution(execution);
      setExecutions(prev => [execution, ...prev]);
    } catch (err) {
      console.error('Failed to save execution:', err);
      setError('Failed to save test execution');
    }
  };

  const deleteExecution = async (executionId: string) => {
    try {
      await persistenceService.deleteExecution(executionId);
      setExecutions(prev => prev.filter(exec => exec.id !== executionId));
    } catch (err) {
      console.error('Failed to delete execution:', err);
      setError('Failed to delete execution');
    }
  };

  const exportData = async () => {
    try {
      return await persistenceService.exportData();
    } catch (err) {
      console.error('Failed to export data:', err);
      throw new Error('Failed to export data');
    }
  };

  const importData = async (data: { datasets: TestDataset[]; executions: TestExecution[] }) => {
    try {
      await persistenceService.importData(data);
      setDatasets(data.datasets);
      setExecutions(data.executions);
    } catch (err) {
      console.error('Failed to import data:', err);
      throw new Error('Failed to import data');
    }
  };

  const getStorageStats = async () => {
    try {
      return await persistenceService.getStorageStats();
    } catch (err) {
      console.error('Failed to get storage stats:', err);
      return { datasets: 0, executions: 0, totalSize: 0 };
    }
  };

  return {
    datasets,
    executions,
    isLoading,
    error,
    addDataset,
    updateDataset,
    deleteDataset,
    addExecution,
    deleteExecution,
    exportData,
    importData,
    getStorageStats
  };
};
