
import React from 'react';
import { Dashboard } from '@/components/Dashboard';
import { useErgonomicData } from '@/hooks/useErgonomicData';

const Index = () => {
  const { datasets, executions, addDataset, updateDataset, addExecution } = useErgonomicData();

  return (
    <Dashboard
      datasets={datasets}
      executions={executions}
      onAddDataset={addDataset}
      onUpdateDataset={updateDataset}
      onRunTest={addExecution}
    />
  );
};

export default Index;
