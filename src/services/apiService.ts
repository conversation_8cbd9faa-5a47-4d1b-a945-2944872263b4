
import type { ParameterDefinitions, APIConfiguration, TestResult } from '@/types/ergonomic';

// Define interfaces for the three API responses
interface API1Response {
  workstation_setup: {
    proper_workstation: boolean;
    proper_side_profile: boolean;
    facing_direction: string;
    legs_visible: boolean;
  };
  posture_analysis: {
    sitting_posture: string;
    sitting_height: string;
  };
}

interface API2Response {
  desk_setup: {
    desk_height: string;
    armrest_position: string;
    seatpan_spacing: string;
  };
  equipment_count: {
    monitors: number;
    laptops: number;
    keyboards: number;
    mice: number;
  };
}

interface API3Response {
  device_status: {
    laptop_status: string;
    laptop_usage: string;
  };
  positioning: {
    screen_distance: string;
    laptop_distance: string;
    screen_height: string;
    laptop_height: string;
    keyboard_placement: string;
    mouse_placement: string;
  };
}

export class MultiAPIService {
  private api1Config: APIConfiguration;
  private api2Config: APIConfiguration;
  private api3Config: APIConfiguration;

  constructor(
    api1Config: APIConfiguration,
    api2Config: APIConfiguration,
    api3Config: APIConfiguration
  ) {
    this.api1Config = api1Config;
    this.api2Config = api2Config;
    this.api3Config = api3Config;
  }

  // Call all three APIs for a single image
  async evaluateImage(imageUrl: string, imageId: string): Promise<ParameterDefinitions> {
    try {
      // Call all APIs in parallel
      const [api1Response, api2Response, api3Response] = await Promise.all([
        this.callAPI1(imageUrl),
        this.callAPI2(imageUrl),
        this.callAPI3(imageUrl)
      ]);

      // Transform and combine responses
      return this.transformResponses(api1Response, api2Response, api3Response);
    } catch (error) {
      console.error(`Error evaluating image ${imageId}:`, error);
      throw error;
    }
  }

  // API 1: Workstation and posture analysis
  private async callAPI1(imageUrl: string): Promise<API1Response> {
    const imageFieldName = this.getImageFieldName(this.api1Config.endpoint);
    const response = await this.makeRequest(this.api1Config, { [imageFieldName]: imageUrl });
    return response as API1Response;
  }

  // API 2: Desk setup and equipment counting
  private async callAPI2(imageUrl: string): Promise<API2Response> {
    const imageFieldName = this.getImageFieldName(this.api2Config.endpoint);
    const response = await this.makeRequest(this.api2Config, { [imageFieldName]: imageUrl });
    return response as API2Response;
  }

  // API 3: Device positioning and placement
  private async callAPI3(imageUrl: string): Promise<API3Response> {
    const imageFieldName = this.getImageFieldName(this.api3Config.endpoint);
    const response = await this.makeRequest(this.api3Config, { [imageFieldName]: imageUrl });
    return response as API3Response;
  }

  // Generic API request method
  private async makeRequest(config: APIConfiguration, payload: any): Promise<any> {
    const headers: Record<string, string> = { ...config.headers };

    // Add authentication
    if (config.authType === 'bearer' && config.authToken) {
      headers['Authorization'] = `Bearer ${config.authToken}`;
    } else if (config.authType === 'apiKey' && config.authToken) {
      headers['X-API-Key'] = config.authToken;
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout);

    try {
      const response = await fetch(config.endpoint, {
        method: config.method,
        headers,
        body: JSON.stringify(payload),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Determines the correct field name for the image based on the API endpoint
   * APIs at aiec2.synergopro.com expect 'image' field
   * Other APIs might expect 'image_url' field
   */
  private getImageFieldName(apiEndpoint: string): string {
    // Check if this is one of the aiec2.synergopro.com APIs
    if (apiEndpoint.includes('aiec2.synergopro.com')) {
      return 'image';
    }

    // Default to image_url for other APIs
    return 'image_url';
  }

  // Transform the three API responses into our ParameterDefinitions format
  private transformResponses(
    api1: API1Response,
    api2: API2Response,
    api3: API3Response
  ): ParameterDefinitions {
    return {
      // From API 1
      properWorkstation: api1.workstation_setup.proper_workstation,
      properSideProfileImage: api1.workstation_setup.proper_side_profile,
      facingSide: this.mapFacingDirection(api1.workstation_setup.facing_direction),
      legsVisible: api1.workstation_setup.legs_visible,
      posture: this.mapPosture(api1.posture_analysis.sitting_posture),
      sittingHeight: this.mapSittingHeight(api1.posture_analysis.sitting_height),

      // From API 2
      deskHeight: this.mapDeskHeight(api2.desk_setup.desk_height),
      armrest: this.mapArmrest(api2.desk_setup.armrest_position),
      seatpan: this.mapSeatpan(api2.desk_setup.seatpan_spacing),
      numberOfMonitors: api2.equipment_count.monitors,
      numberOfLaptops: api2.equipment_count.laptops,
      keyboard: api2.equipment_count.keyboards,
      mouse: api2.equipment_count.mice,

      // From API 3
      laptopStatus: this.mapLaptopStatus(api3.device_status.laptop_status),
      laptopUsage: this.mapLaptopUsage(api3.device_status.laptop_usage),
      screenDistance: this.mapDistance(api3.positioning.screen_distance),
      laptopDistance: this.mapDistance(api3.positioning.laptop_distance),
      screenHeight: this.mapHeight(api3.positioning.screen_height),
      laptopHeight: this.mapHeight(api3.positioning.laptop_height),
      keyboardPlacement: this.mapPlacement(api3.positioning.keyboard_placement),
      mousePlacement: this.mapPlacement(api3.positioning.mouse_placement)
    };
  }

  // Mapping functions to convert API responses to our enum values
  private mapFacingDirection(direction: string): 'left' | 'right' | 'front' | 'back' {
    const mapping: Record<string, 'left' | 'right' | 'front' | 'back'> = {
      'left': 'left',
      'right': 'right',
      'front': 'front',
      'back': 'back',
      'profile_left': 'left',
      'profile_right': 'right'
    };
    return mapping[direction.toLowerCase()] || 'front';
  }

  private mapPosture(posture: string): 'Sitting back' | 'perched forward' | 'slouching' | 'leaning forward' {
    const mapping: Record<string, 'Sitting back' | 'perched forward' | 'slouching' | 'leaning forward'> = {
      'sitting_back': 'Sitting back',
      'perched_forward': 'perched forward',
      'slouching': 'slouching',
      'leaning_forward': 'leaning forward'
    };
    return mapping[posture.toLowerCase()] || 'Sitting back';
  }

  private mapSittingHeight(height: string): 'hips lower than knees' | 'hips inline with knees' | 'Hips higher than knees legs grounded' | 'Hips higher than knees legs hanging' {
    const mapping = {
      'hips_lower': 'hips lower than knees',
      'hips_inline': 'hips inline with knees',
      'hips_higher_grounded': 'Hips higher than knees legs grounded',
      'hips_higher_hanging': 'Hips higher than knees legs hanging'
    } as const;
    return mapping[height.toLowerCase() as keyof typeof mapping] || 'hips inline with knees';
  }

  private mapDeskHeight(height: string): 'inline with elbow' | 'above elbow' | 'below elbow' {
    const mapping = {
      'inline': 'inline with elbow',
      'above': 'above elbow',
      'below': 'below elbow'
    } as const;
    return mapping[height.toLowerCase() as keyof typeof mapping] || 'inline with elbow';
  }

  private mapArmrest(position: string): 'above elbow' | 'below elbow' | 'inline with elbow' | 'no armrests' {
    const mapping = {
      'above': 'above elbow',
      'below': 'below elbow',
      'inline': 'inline with elbow',
      'none': 'no armrests'
    } as const;
    return mapping[position.toLowerCase() as keyof typeof mapping] || 'no armrests';
  }

  private mapSeatpan(spacing: string): '2 finger spacing' | 'More than 2 finger spacing' | 'less than 2 finger spacing' {
    const mapping = {
      '2_finger': '2 finger spacing',
      'more_than_2': 'More than 2 finger spacing',
      'less_than_2': 'less than 2 finger spacing'
    } as const;
    return mapping[spacing.toLowerCase() as keyof typeof mapping] || '2 finger spacing';
  }

  private mapLaptopStatus(status: string): 'open' | 'closed' {
    return status.toLowerCase() === 'open' ? 'open' : 'closed';
  }

  private mapLaptopUsage(usage: string): 'open' | 'docked' {
    return usage.toLowerCase() === 'docked' ? 'docked' : 'open';
  }

  private mapDistance(distance: string): 'At arms length' | 'Less than arms length' | 'More than arms length' {
    const mapping = {
      'at_arms_length': 'At arms length',
      'less_than_arms': 'Less than arms length',
      'more_than_arms': 'More than arms length'
    } as const;
    return mapping[distance.toLowerCase() as keyof typeof mapping] || 'At arms length';
  }

  private mapHeight(height: string): 'below eye level' | 'at eye level' | 'above eye level' {
    const mapping = {
      'below': 'below eye level',
      'at': 'at eye level',
      'above': 'above eye level'
    } as const;
    return mapping[height.toLowerCase() as keyof typeof mapping] || 'at eye level';
  }

  private mapPlacement(placement: string): 'Palm distance from edge' | 'Extended and reaching forward' | 'Edge of the table' {
    const mapping = {
      'palm_distance': 'Palm distance from edge',
      'extended_reaching': 'Extended and reaching forward',
      'edge_table': 'Edge of the table'
    } as const;
    return mapping[placement.toLowerCase() as keyof typeof mapping] || 'Palm distance from edge';
  }

  // Compare API results with ground truth
  static compareResults(
    imageId: string,
    expected: ParameterDefinitions,
    actual: ParameterDefinitions
  ): TestResult[] {
    const results: TestResult[] = [];

    Object.keys(expected).forEach(parameter => {
      const key = parameter as keyof ParameterDefinitions;
      const expectedValue = expected[key];
      const actualValue = actual[key];

      results.push({
        imageId,
        parameter,
        expected: expectedValue,
        actual: actualValue,
        passed: this.valuesMatch(expectedValue, actualValue),
        confidence: 0.95 // This could come from the API if available
      });
    });

    return results;
  }

  private static valuesMatch(expected: any, actual: any): boolean {
    if (typeof expected === 'boolean' && typeof actual === 'boolean') {
      return expected === actual;
    }
    if (typeof expected === 'number' && typeof actual === 'number') {
      return expected === actual;
    }
    if (typeof expected === 'string' && typeof actual === 'string') {
      return expected.toLowerCase().trim() === actual.toLowerCase().trim();
    }
    return false;
  }
}
