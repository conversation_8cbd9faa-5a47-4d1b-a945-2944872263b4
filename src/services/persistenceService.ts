
import type { TestDataset, TestExecution } from '@/types/ergonomic';

// SQLite persistence service using IndexedDB for browser storage
export class PersistenceService {
  private dbName = 'ergonomic_test_db';
  private dbVersion = 1;
  private db: IDBDatabase | null = null;

  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create datasets table
        if (!db.objectStoreNames.contains('datasets')) {
          const datasetsStore = db.createObjectStore('datasets', { keyPath: 'id' });
          datasetsStore.createIndex('name', 'name', { unique: false });
          datasetsStore.createIndex('createdAt', 'createdAt', { unique: false });
        }

        // Create executions table
        if (!db.objectStoreNames.contains('executions')) {
          const executionsStore = db.createObjectStore('executions', { keyPath: 'id' });
          executionsStore.createIndex('datasetId', 'datasetId', { unique: false });
          executionsStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        // Create settings table for API configurations
        if (!db.objectStoreNames.contains('settings')) {
          db.createObjectStore('settings', { keyPath: 'key' });
        }
      };
    });
  }

  // Dataset operations
  async saveDataset(dataset: TestDataset): Promise<void> {
    return this.performTransaction('datasets', 'readwrite', (store) => {
      store.put(dataset);
    });
  }

  async getDatasets(): Promise<TestDataset[]> {
    return this.performQuery('datasets', 'readonly', (store) => {
      return store.getAll();
    });
  }

  async getDataset(id: string): Promise<TestDataset | null> {
    return this.performQuery('datasets', 'readonly', (store) => {
      return store.get(id);
    });
  }

  async deleteDataset(id: string): Promise<void> {
    return this.performTransaction('datasets', 'readwrite', (store) => {
      store.delete(id);
    });
  }

  // Execution operations
  async saveExecution(execution: TestExecution): Promise<void> {
    return this.performTransaction('executions', 'readwrite', (store) => {
      store.put(execution);
    });
  }

  async getExecutions(): Promise<TestExecution[]> {
    return this.performQuery('executions', 'readonly', (store) => {
      return store.getAll();
    });
  }

  async getExecutionsByDataset(datasetId: string): Promise<TestExecution[]> {
    return this.performQuery('executions', 'readonly', (store) => {
      const index = store.index('datasetId');
      return index.getAll(datasetId);
    });
  }

  async deleteExecution(id: string): Promise<void> {
    return this.performTransaction('executions', 'readwrite', (store) => {
      store.delete(id);
    });
  }

  // Settings operations
  async saveSetting(key: string, value: any): Promise<void> {
    return this.performTransaction('settings', 'readwrite', (store) => {
      store.put({ key, value });
    });
  }

  async getSetting(key: string): Promise<any> {
    const result = await this.performQuery('settings', 'readonly', (store) => {
      return store.get(key);
    });
    return result?.value || null;
  }

  // Utility methods
  private async performTransaction(
    storeName: string,
    mode: IDBTransactionMode,
    operation: (store: IDBObjectStore) => void
  ): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], mode);
      const store = transaction.objectStore(storeName);

      transaction.oncomplete = () => resolve();
      transaction.onerror = () => reject(transaction.error);

      operation(store);
    });
  }

  private async performQuery<T>(
    storeName: string,
    mode: IDBTransactionMode,
    operation: (store: IDBObjectStore) => IDBRequest<T>
  ): Promise<T> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], mode);
      const store = transaction.objectStore(storeName);
      const request = operation(store);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // Data export/import for backup
  async exportData(): Promise<{ datasets: TestDataset[]; executions: TestExecution[] }> {
    const datasets = await this.getDatasets();
    const executions = await this.getExecutions();
    return { datasets, executions };
  }

  async importData(data: { datasets: TestDataset[]; executions: TestExecution[] }): Promise<void> {
    // Clear existing data
    await this.clearAllData();

    // Import datasets
    for (const dataset of data.datasets) {
      await this.saveDataset(dataset);
    }

    // Import executions
    for (const execution of data.executions) {
      await this.saveExecution(execution);
    }
  }

  private async clearAllData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const storeNames = ['datasets', 'executions', 'settings'];
    
    for (const storeName of storeNames) {
      await this.performTransaction(storeName, 'readwrite', (store) => {
        store.clear();
      });
    }
  }

  // Database statistics
  async getStorageStats(): Promise<{ datasets: number; executions: number; totalSize: number }> {
    const datasets = await this.getDatasets();
    const executions = await this.getExecutions();
    
    // Estimate size (rough calculation)
    const totalSize = JSON.stringify({ datasets, executions }).length;

    return {
      datasets: datasets.length,
      executions: executions.length,
      totalSize
    };
  }
}

// Create singleton instance
export const persistenceService = new PersistenceService();
