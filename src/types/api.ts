
export interface APIEndpoint {
  id: string;
  name: string;
  url: string;
  method: 'POST' | 'PUT' | 'GET';
  headers: Record<string, string>;
  description?: string;
  category?: string;
}

export interface SoloTestResult {
  success: boolean;
  data: any;
  annotatedImageUrl?: string;
  error?: string;
  timestamp: string;
  responseTime: number;
}

export interface SoloTestExecution {
  id: string;
  apiId: string;
  imageUrl: string;
  result: SoloTestResult;
  timestamp: string;
}
