
export interface ParameterDefinitions {
  properWorkstation: boolean;
  properSideProfileImage: boolean;
  facingSide: 'left' | 'right' | 'front' | 'back';
  legsVisible: boolean;
  posture: 'Sitting back' | 'perched forward' | 'slouching' | 'leaning forward';
  sittingHeight: 'hips lower than knees' | 'hips inline with knees' | 'Hips higher than knees legs grounded' | 'Hips higher than knees legs hanging';
  deskHeight: 'inline with elbow' | 'above elbow' | 'below elbow';
  armrest: 'above elbow' | 'below elbow' | 'inline with elbow' | 'no armrests';
  seatpan: '2 finger spacing' | 'More than 2 finger spacing' | 'less than 2 finger spacing';
  numberOfMonitors: number;
  numberOfLaptops: number;
  keyboard: number;
  mouse: number;
  laptopStatus: 'open' | 'closed';
  laptopUsage: 'open' | 'docked';
  screenDistance: 'At arms length' | 'Less than arms length' | 'More than arms length';
  laptopDistance: 'At arms length' | 'Less than arms length' | 'More than arms length';
  screenHeight: 'below eye level' | 'at eye level' | 'above eye level';
  laptopHeight: 'below eye level' | 'at eye level' | 'above eye level';
  keyboardPlacement: 'Palm distance from edge' | 'Extended and reaching forward' | 'Edge of the table';
  mousePlacement: 'Palm distance from edge' | 'Extended and reaching forward' | 'Edge of the table';
}

export interface TestImage {
  id: string;
  url: string;
  name: string;
  groundTruth: ParameterDefinitions;
  metadata?: {
    addedBy: string;
    addedAt: string;
    notes?: string;
  };
}

export interface TestDataset {
  id: string;
  name: string;
  images: TestImage[];
  createdAt: string;
  updatedAt: string;
}

export interface APIConfiguration {
  endpoint: string;
  method: 'POST' | 'PUT';
  headers: { [key: string]: string };
  authType: 'none' | 'bearer' | 'apiKey';
  authToken?: string;
  timeout: number;
}

export interface TestResult {
  imageId: string;
  parameter: string;
  expected: any;
  actual: any;
  passed: boolean;
  confidence?: number;
  error?: string;
}

export interface ExecutionSummary {
  totalImages: number;
  totalParameters: number;
  overallPassRate: number;
  imagePassRates: { [imageId: string]: number };
  parameterPassRates: { [parameter: string]: number };
}

export interface TestExecution {
  id: string;
  datasetId: string;
  apiConfig: APIConfiguration;
  results: TestResult[];
  summary: ExecutionSummary;
  timestamp: string;
}
